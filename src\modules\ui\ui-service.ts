/**
 * UI Service - Panel window management and animations
 * Migrated from C# SideView.UI.Services.UIService
 */

import { BrowserWindow, screen, ipcMain } from 'electron';
import * as path from 'path';
import { AppEventBus } from '@modules/core/event-bus';
import { ConfigurationService } from '@modules/core/configuration-service';
import { PanelPosition, AppSettings } from '@shared/types/app.types';
import { UIModuleStartedEvent, UIModuleStoppedEvent, PanelShownEvent, PanelHiddenEvent, ActivationEventArgs } from '@shared/types/events.types';
import { ActivationEngine } from './activation-engine';
import { ThemeManager, ThemeChangedEventArgs } from './theme-manager';
import { WebEngineService } from '@modules/webengine/webengine-service';

export class UIService {
  private readonly eventBus: AppEventBus;
  private readonly configurationService: ConfigurationService;
  private readonly logger: Console;
  private webEngineService?: WebEngineService;

  private panelWindow?: BrowserWindow;
  private isVisible = false;
  private isPinned = false;
  private isAnimating = false;
  private activationEngine?: ActivationEngine;
  private themeManager?: ThemeManager;

  // Hardcoded panel dimensions for consistency - no more dynamic sizing
  private static readonly PANEL_WIDTH = 480; // Updated from 280px to 480px
  private static readonly PANEL_HEIGHT = 600;
  private static readonly PANEL_HEADER_HEIGHT = 60; // Actual height: padding(28px) + content(~32px)
  private static readonly TAB_BAR_HEIGHT = 56; // Actual height: min-height(44px) + padding(12px)
  private static readonly TOTAL_UI_HEIGHT = UIService.PANEL_HEADER_HEIGHT + UIService.TAB_BAR_HEIGHT;
  
  // Modal state management
  private isModalOpen = false;
  private detachedBrowserView: any = null;
  private isSettingSize = false; // Flag to prevent infinite resize loops

  // Event listener cleanup tracking
  private panelWindowEventListeners: Map<string, (...args: any[]) => void> = new Map();

  constructor(
    eventBus: AppEventBus,
    configurationService: ConfigurationService,
    logger: Console = console
  ) {
    this.eventBus = eventBus;
    this.configurationService = configurationService;
    this.logger = logger;
  }

  setWebEngineService(webEngineService: WebEngineService): void {
    this.webEngineService = webEngineService;
  }

  async initialize(): Promise<void> {
    this.logger.info('Initializing UI Module');

    try {
      await this.createPanelWindow();
      this.setupActivationEngine();
      this.setupThemeManager();
      this.setupEventHandlers();

      await this.eventBus.publish(new UIModuleStartedEvent());
      this.logger.info('UI Module initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize UI Module:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    console.log('[UI-MODULE] Starting UI Module');

    if (this.activationEngine) {
      console.log('[UI-MODULE] Starting activation engine...');
      await this.activationEngine.start();
    }

    if (this.themeManager) {
      console.log('[UI-MODULE] Initializing theme manager...');
      await this.themeManager.initialize();
    }

    await this.eventBus.publish(new UIModuleStartedEvent());
    console.log('[SETTINGS-DEBUG] Settings dialog functionality initialized');
    console.log('[UI-MODULE] UI Module started successfully');
  }

  async stop(): Promise<void> {
    this.logger.info('Stopping UI Module');

    try {
      // Clear modal state first
      this.isModalOpen = false;
      this.detachedBrowserView = null;

      // Clean up panel window event listeners
      this.cleanupPanelWindowEventListeners();

      // Clean up activation engine
      if (this.activationEngine) {
        await this.activationEngine.stop();
        delete (this as any).activationEngine;
      }

      // Clean up theme manager
      if (this.themeManager) {
        // ThemeManager doesn't have a stop method, just remove reference
        delete (this as any).themeManager;
      }

      // Clean up IPC handlers
      try {
        ipcMain.removeHandler('ui:setPinned');
        ipcMain.removeHandler('ui:isVisible');
        ipcMain.removeHandler('ui:isPinned');
        ipcMain.removeHandler('ui:setModalState');
        ipcMain.removeHandler('ui:restartWidget');
        ipcMain.removeHandler('ui:attachActiveWebApp');
        ipcMain.removeAllListeners('activation-click');
      } catch (error) {
        this.logger.warn('Some IPC handlers were already removed:', error);
      }

      // Close panel window
      if (this.panelWindow && !this.panelWindow.isDestroyed()) {
        this.panelWindow.close();
      }

      await this.eventBus.publish(new UIModuleStoppedEvent());
      this.logger.info('UI Module stopped successfully');

    } catch (error) {
      this.logger.error('Error during UI Module stop:', error);
      throw error;
    }
  }

  async showPanel(isPreview: boolean = false, animate = true): Promise<void> {
    if (!this.panelWindow || this.isAnimating) {
      return;
    }

    if (this.isVisible) {
      this.logger.debug('Panel is already visible');
      return;
    }

    try {
      this.isAnimating = true;
      
      this.logger.info(`🎬 Showing panel (animated: ${animate}, preview: ${isPreview})`);

      const settings = this.configurationService.getSettings();

      // AGGRESSIVELY enforce hardcoded dimensions - this should prevent size drift
      await this.enforceHardcodedDimensions();
      
      const targetPosition = this.calculateVisiblePosition(settings);

      if (animate) {
        // Shorter animation for preview mode
        const animationDuration = isPreview ? (settings.ui.animationDuration || 300) / 2 : (settings.ui.animationDuration || 300);
        await this.animateToPosition(targetPosition, animationDuration);
      } else {
        this.panelWindow.setPosition(targetPosition.x, targetPosition.y);
        this.panelWindow.show();
      }

      this.isVisible = true;

      // Update activation engine with panel bounds for proper hover detection
      this.updateActivationEnginePanelBounds();

      // Only attach web app if not in modal state
      if (!this.isModalOpen) {
        // Attach the active web app to the panel
        await this.attachActiveWebApp();
      } else {
        this.logger.debug('Skipping web app attachment during panel show: modal is open');
      }

      await this.eventBus.publish(new PanelShownEvent());

      this.logger.info('✅ Panel shown successfully');

    } catch (error) {
      this.logger.error('Failed to show panel:', error);
      throw error;
    } finally {
      this.isAnimating = false;
    }
  }

  async hidePanel(animate = true): Promise<void> {
    if (!this.panelWindow || this.isAnimating || this.isPinned) {
      return;
    }

    if (!this.isVisible) {
      this.logger.debug('Panel is already hidden');
      return;
    }

    try {
      this.isAnimating = true;
      
      // Don't clear dimension cache on hide to maintain consistent sizing
      
      this.logger.info(`🎬 Hiding panel (animated: ${animate})`);

      const settings = this.configurationService.getSettings();
      const targetPosition = this.calculateHiddenPosition(settings);

      // Detach web app before hiding
      await this.detachWebApp();

      if (animate) {
        await this.animateToPosition(targetPosition, settings.ui.animationDuration || 300);
        this.panelWindow.hide();
      } else {
        this.panelWindow.hide();
      }

      this.isVisible = false;
      await this.eventBus.publish(new PanelHiddenEvent());

      this.logger.info('✅ Panel hidden successfully');

    } catch (error) {
      this.logger.error('Failed to hide panel:', error);
      throw error;
    } finally {
      this.isAnimating = false;
    }
  }

  async togglePanel(): Promise<void> {
    if (this.isVisible) {
      await this.hidePanel();
    } else {
      await this.showPanel();
    }
  }

  setPinned(pinned: boolean): void {
    this.isPinned = pinned;
    this.logger.debug(`Panel pin state changed: ${pinned}`);
  }

  getIsVisible(): boolean {
    return this.isVisible;
  }

  getIsPinned(): boolean {
    return this.isPinned;
  }

  // Theme management methods
  getTheme(): { theme: string; isDark: boolean; variables: Record<string, string> } | undefined {
    if (this.themeManager) {
      return {
        theme: this.themeManager.getCurrentTheme(),
        isDark: this.themeManager.getIsDarkMode(),
        variables: this.themeManager.getThemeVariables()
      };
    }
    return undefined;
  }

  async setTheme(theme: string): Promise<void> {
    if (this.themeManager) {
      await this.themeManager.setTheme(theme as any);
    }
  }

  getThemeVariables(): Record<string, string> | undefined {
    if (this.themeManager) {
      return this.themeManager.getThemeVariables();
    }
    return undefined;
  }



  /**
   * Temporarily detach BrowserView for modal dialogs
   */
  // **MODAL-DEBUG: Enhanced Modal State Management with Comprehensive Logging**
  async setModalState(isModalOpen: boolean): Promise<void> {
    console.log(`[MODAL-DEBUG] setModalState called: ${isModalOpen} (current: ${this.isModalOpen})`);

    if (isModalOpen === this.isModalOpen) {
      console.log('[MODAL-DEBUG] Modal state unchanged, skipping');
      return; // No change needed
    }

    // Prevent modal operations during critical panel operations
    if (this.isAnimating) {
      console.log('[MODAL-DEBUG] Skipping modal state change - panel is animating');
      return;
    }

    const previousState = this.isModalOpen;
    this.isModalOpen = isModalOpen;
    console.log(`[MODAL-DEBUG] Modal state updated from ${previousState} to ${isModalOpen}`);

    try {
      if (isModalOpen) {
        console.log('[MODAL-DEBUG] Activating modal state...');
        await this.activateModalState();
      } else {
        console.log('[MODAL-DEBUG] Deactivating modal state...');
        await this.deactivateModalState();
      }

      console.log(`[MODAL-DEBUG] Modal state successfully changed to: ${isModalOpen}`);

    } catch (error) {
      console.error(`[MODAL-DEBUG] Failed to set modal state to ${isModalOpen}:`, error);

      // Revert modal state on error
      this.isModalOpen = previousState;
      console.log(`[MODAL-DEBUG] Reverted modal state to: ${previousState}`);

      // Attempt emergency recovery
      console.log('[MODAL-DEBUG] Attempting emergency recovery...');
      await this.performModalStateEmergencyRecovery();

      throw error;
    }
  }

  // **MODAL-DEBUG: Separate Modal Activation Logic with Detailed Logging**
  private async activateModalState(): Promise<void> {
    console.log('[MODAL-DEBUG] activateModalState starting...');

    try {
      // Step 1: Temporarily detach BrowserView
      console.log('[MODAL-DEBUG] Step 1: Detaching BrowserView...');
      await this.temporaryDetachBrowserView();

      // Step 2: Configure panel window for modal
      console.log('[MODAL-DEBUG] Step 2: Configuring panel window for modal...');
      if (this.panelWindow && !this.panelWindow.isDestroyed()) {
        console.log('[MODAL-DEBUG] Panel window available, setting always on top and focusing...');
        this.panelWindow.setAlwaysOnTop(true);
        this.panelWindow.focus();

        // Force the panel window to be visible and focused
        if (!this.panelWindow.isVisible()) {
          console.log('[MODAL-DEBUG] Panel window not visible, showing...');
          this.panelWindow.show();
        } else {
          console.log('[MODAL-DEBUG] Panel window already visible');
        }
      } else {
        console.log('[MODAL-DEBUG] Panel window not available or destroyed');
      }

      console.log('[MODAL-DEBUG] Modal state activated - BrowserView detached, window focused');

    } catch (error) {
      console.error('[MODAL-DEBUG] Failed to activate modal state:', error);
      throw error;
    }
  }

  // **MODAL-DEBUG: Separate Modal Deactivation Logic with Detailed Logging**
  private async deactivateModalState(): Promise<void> {
    console.log('[MODAL-DEBUG] deactivateModalState starting...');

    try {
      // Step 1: Restore BrowserView
      console.log('[MODAL-DEBUG] Step 1: Restoring BrowserView...');
      await this.restoreBrowserView();

      // Step 2: Reset panel window configuration
      console.log('[MODAL-DEBUG] Step 2: Resetting panel window configuration...');
      if (this.panelWindow && !this.panelWindow.isDestroyed()) {
        console.log('[MODAL-DEBUG] Setting panel window always on top to false...');
        this.panelWindow.setAlwaysOnTop(false);
      } else {
        console.log('[MODAL-DEBUG] Panel window not available for configuration reset');
      }

      // Step 3: Wait for state to settle
      console.log('[MODAL-DEBUG] Step 3: Waiting for state to settle...');
      await new Promise(resolve => setTimeout(resolve, 150));

      // Step 4: Force UI refresh with enhanced error handling
      console.log('[MODAL-DEBUG] Step 4: Forcing UI refresh after modal...');
      await this.forceUIRefreshAfterModal();

      console.log('[MODAL-DEBUG] Modal state deactivated - BrowserView restored with cleanup');

    } catch (error) {
      console.error('[MODAL-DEBUG] Failed to deactivate modal state:', error);
      throw error;
    }
  }

  // **CRITICAL FIX 13: Enhanced UI Refresh After Modal**
  private async forceUIRefreshAfterModal(): Promise<void> {
    if (!this.panelWindow || this.panelWindow.isDestroyed()) {
      this.logger.warn('⚠️ Panel window not available for UI refresh');
      return;
    }

    try {
      // Multiple refresh strategies for maximum compatibility
      await this.panelWindow.webContents.executeJavaScript(`
        try {
          // Strategy 1: Force repaint
          document.body.style.display = 'none';
          document.body.offsetHeight; // Trigger reflow
          document.body.style.display = '';

          // Strategy 2: Force style recalculation
          document.documentElement.style.transform = 'translateZ(0)';
          setTimeout(() => {
            document.documentElement.style.transform = '';
          }, 10);

          // Strategy 3: Dispatch resize event to trigger layout updates
          window.dispatchEvent(new Event('resize'));

          console.log('✅ Modal cleanup UI refresh completed');
          'modal-cleanup-complete';
        } catch (refreshError) {
          console.error('❌ Modal cleanup UI refresh failed:', refreshError);
          'modal-cleanup-failed';
        }
      `);

      this.logger.debug('✅ UI refresh after modal completed');

    } catch (jsError) {
      this.logger.warn('⚠️ Failed to execute modal cleanup script:', jsError);
      // Don't throw here - this is a nice-to-have enhancement
    }
  }

  // **CRITICAL FIX 14: Emergency Recovery for Modal State**
  private async performModalStateEmergencyRecovery(): Promise<void> {
    this.logger.warn('🚨 Performing modal state emergency recovery...');

    try {
      // Step 1: Force clear modal state
      this.isModalOpen = false;
      this.detachedBrowserView = null;

      // Step 2: Reset panel window state
      if (this.panelWindow && !this.panelWindow.isDestroyed()) {
        this.panelWindow.setAlwaysOnTop(false);
      }

      // Step 3: Try to restore normal state
      try {
        await this.restoreBrowserView();
      } catch (restoreError) {
        this.logger.error('❌ Failed to restore BrowserView during emergency recovery:', restoreError);
      }

      // Step 4: Force UI refresh
      try {
        await this.forceUIRefreshAfterModal();
      } catch (refreshError) {
        this.logger.error('❌ Failed to refresh UI during emergency recovery:', refreshError);
      }

      this.logger.info('✅ Modal state emergency recovery completed');

    } catch (error) {
      this.logger.error('❌ Modal state emergency recovery failed:', error);
      // Don't throw here - we've done our best
    }
  }

  /**
   * Temporarily detach BrowserView (for modals)
   */
  private async temporaryDetachBrowserView(): Promise<void> {
    console.log('[MODAL-DEBUG] temporaryDetachBrowserView starting...');

    if (!this.panelWindow) {
      console.log('[MODAL-DEBUG] Cannot detach BrowserView: panel window not available');
      return;
    }

    try {
      // Store reference to currently attached BrowserView
      console.log('[MODAL-DEBUG] Getting current BrowserView from panel window...');
      const currentBrowserView = this.panelWindow.getBrowserView();
      if (currentBrowserView) {
        console.log('[MODAL-DEBUG] Found BrowserView to detach, storing reference...');
        this.detachedBrowserView = currentBrowserView;

        // Safely detach the BrowserView
        console.log('[MODAL-DEBUG] Detaching BrowserView from panel window...');
        this.panelWindow.setBrowserView(null);

        // Force a small delay to ensure detachment is complete
        console.log('[MODAL-DEBUG] Waiting for detachment to complete...');
        await new Promise(resolve => setTimeout(resolve, 10));

        console.log('[MODAL-DEBUG] BrowserView successfully detached for modal dialog');
      } else {
        console.log('[MODAL-DEBUG] No BrowserView currently attached to detach');
      }
    } catch (error) {
      console.error('[MODAL-DEBUG] Failed to temporarily detach BrowserView:', error);
      // Clear reference on error to prevent memory leaks
      this.detachedBrowserView = null;
    }
  }

  /**
   * Restore previously detached BrowserView
   */
  private async restoreBrowserView(): Promise<void> {
    console.log('[MODAL-DEBUG] restoreBrowserView starting...');

    if (!this.panelWindow) {
      console.log('[MODAL-DEBUG] Cannot restore BrowserView: panel window not available');
      return;
    }

    if (!this.detachedBrowserView) {
      console.log('[MODAL-DEBUG] No detached BrowserView to restore');
      return;
    }

    try {
      // Use hardcoded dimensions for consistent bounds calculation
      console.log('[MODAL-DEBUG] Calculating bounds for BrowserView restoration...');
      const panelWidth = UIService.PANEL_WIDTH;
      const panelHeight = UIService.PANEL_HEIGHT;
      const totalUIHeight = UIService.TOTAL_UI_HEIGHT;

      const webContentBounds = {
        x: 0,
        y: totalUIHeight,
        width: panelWidth,
        height: Math.max(0, panelHeight - totalUIHeight)
      };

      console.log(`[MODAL-DEBUG] Calculated bounds: ${JSON.stringify(webContentBounds)}`);

      // Validate bounds before applying
      if (webContentBounds.width <= 0 || webContentBounds.height <= 0) {
        console.error('[MODAL-DEBUG] Invalid web content bounds for restore:', webContentBounds);
        this.detachedBrowserView = null;
        return;
      }

      // Restore the BrowserView with proper bounds
      console.log('[MODAL-DEBUG] Restoring BrowserView to panel window...');
      this.panelWindow.setBrowserView(this.detachedBrowserView);

      // Apply bounds with a small delay to ensure proper attachment
      console.log('[MODAL-DEBUG] Waiting for attachment to complete...');
      await new Promise(resolve => setTimeout(resolve, 10));

      console.log('[MODAL-DEBUG] Setting BrowserView bounds...');
      this.detachedBrowserView.setBounds(webContentBounds);

      // Clear the reference
      console.log('[MODAL-DEBUG] Clearing detached BrowserView reference...');
      this.detachedBrowserView = null;

      console.log('[MODAL-DEBUG] BrowserView successfully restored after modal dialog closed');
    } catch (error) {
      console.error('[MODAL-DEBUG] Failed to restore BrowserView:', error);
      // Clear the reference even on error to prevent memory leaks
      this.detachedBrowserView = null;

      // **ENHANCED ERROR RECOVERY**: Try emergency restoration
      console.log('[MODAL-DEBUG] Attempting emergency BrowserView restoration...');
      try {
        await this.emergencyBrowserViewRestore();
      } catch (emergencyError) {
        console.error('[MODAL-DEBUG] Emergency BrowserView restore also failed:', emergencyError);
      }

      // Try to clear any problematic state
      console.log('[MODAL-DEBUG] Clearing BrowserView state after error...');
      try {
        this.panelWindow.setBrowserView(null);
      } catch (clearError) {
        console.error('[MODAL-DEBUG] Failed to clear BrowserView after restore error:', clearError);
      }
    }
  }

  /**
   * Emergency BrowserView restoration when normal restore fails
   */
  private async emergencyBrowserViewRestore(): Promise<void> {
    this.logger.warn('🚨 Performing emergency BrowserView restoration...');

    try {
      // Step 1: Clear any existing BrowserView
      if (this.panelWindow && !this.panelWindow.isDestroyed()) {
        this.panelWindow.setBrowserView(null);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Step 2: Get current active WebApp from WebEngineService
      const activeWebAppHost = this.webEngineService?.getActiveWebAppHost();
      if (!activeWebAppHost) {
        this.logger.debug('No active WebApp available for emergency restore');
        return;
      }

      // Step 3: Force reattach with hardcoded bounds
      const emergencyBounds = {
        x: 0,
        y: UIService.TOTAL_UI_HEIGHT,
        width: UIService.PANEL_WIDTH,
        height: Math.max(0, UIService.PANEL_HEIGHT - UIService.TOTAL_UI_HEIGHT)
      };

      const browserView = activeWebAppHost.BrowserView;
      if (!browserView) {
        this.logger.warn('No BrowserView available in active WebAppHost');
        return;
      }

      if (!this.panelWindow || this.panelWindow.isDestroyed()) {
        this.logger.warn('Panel window not available for emergency restore');
        return;
      }

      this.panelWindow.setBrowserView(browserView);
      browserView.setBounds(emergencyBounds);

      // Step 4: Force focus and visibility
      if (browserView.webContents && !browserView.webContents.isDestroyed()) {
        browserView.webContents.focus();
      }

      this.logger.info('✅ Emergency BrowserView restoration completed');

    } catch (error) {
      this.logger.error('❌ Emergency BrowserView restoration failed:', error);
      throw error;
    }
  }

  /**
   * Restart the widget to ensure clean state after settings changes
   */
  async restartWidget(): Promise<void> {
    this.logger.info('🔄 Restarting widget for clean state restoration...');

    try {
      // First, ensure modal state is cleared
      this.isModalOpen = false;
      this.detachedBrowserView = null;

      // Detach any current web app
      await this.detachWebApp();

      // Hide panel if visible
      if (this.isVisible) {
        await this.hidePanel(false); // No animation for restart
      }

      // Force panel size enforcement with enhanced protection
      await this.enforceHardcodedDimensions();

      // Reload the panel renderer to ensure clean state
      if (this.panelWindow && !this.panelWindow.isDestroyed()) {
        await this.panelWindow.webContents.reload();
        this.logger.debug('Widget restart: panel renderer reloaded');

        // Wait for renderer to be ready
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Reattach active web app if available
      await this.attachActiveWebApp();

      this.logger.info('✅ Widget restart completed successfully');

    } catch (error) {
      this.logger.error('Failed to restart widget:', error);
      throw error;
    }
  }

  private async createPanelWindow(): Promise<void> {
    const settings = this.configurationService.getSettings();
    
    const hiddenPosition = this.calculateHiddenPosition(settings);

    // Preload script configuration
    const preloadPath = path.join(__dirname, '../renderer/preload.js');

    // Use hardcoded dimensions for window creation
    const panelWidth = UIService.PANEL_WIDTH;
    const panelHeight = UIService.PANEL_HEIGHT;

    this.panelWindow = new BrowserWindow({
      width: panelWidth,
      height: panelHeight,
      minWidth: panelWidth,
      maxWidth: panelWidth,
      minHeight: panelHeight,
      maxHeight: panelHeight,
      x: hiddenPosition.x,
      y: hiddenPosition.y,
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      movable: false,
      minimizable: false,
      maximizable: false,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: preloadPath,
        devTools: true // Enable dev tools for debugging
      }
    });

    // Load the panel HTML
    const panelHtmlPath = path.join(__dirname, '../renderer/panel.html');
    this.logger.info('Loading panel HTML from:', panelHtmlPath);

    try {
      await this.panelWindow.loadFile(panelHtmlPath);
      this.logger.info('Panel HTML loaded successfully');

      // Verify renderer initialization and test settings dialog
      setTimeout(async () => {
        try {
          const result = await this.panelWindow?.webContents.executeJavaScript(`
            !!window.PanelRenderer && !!window.panelRendererInstance
          `);
          if (result) {
            this.logger.info('✅ Panel renderer initialized successfully');

            // Test settings dialog IPC communication
            try {
              const testConfig = await this.panelWindow?.webContents.executeJavaScript(`
                window.sideView.config.get()
              `);
              this.logger.info('✅ Settings dialog IPC communication working:', !!testConfig);
            } catch (configError) {
              this.logger.error('❌ Settings dialog IPC communication failed:', configError);
            }
          } else {
            this.logger.warn('⚠️ Panel renderer not fully initialized');
          }
        } catch (error) {
          this.logger.error('Failed to verify renderer initialization:', error);
        }
      }, 500);

    } catch (error) {
      this.logger.error('Failed to load panel HTML:', error);
      throw error;
    }

    // Add debugging to check if renderer is loading
    this.panelWindow.webContents.on('dom-ready', () => {
      this.logger.info('Panel DOM ready - renderer should be initializing');
    });

    this.panelWindow.webContents.on('did-finish-load', () => {
      this.logger.info('Panel finished loading - checking renderer status');

      // Check if renderer is working by executing a simple script
      this.panelWindow?.webContents.executeJavaScript(`
        console.log('Panel webContents script execution test');
        window.rendererStatus = 'loaded';
        if (window.sideView) {
          console.log('window.sideView is available:', Object.keys(window.sideView));
        } else {
          console.log('window.sideView is NOT available - preload issue');
        }
        'renderer-check-complete';
      `).then((result) => {
        this.logger.info('Renderer check result:', result);
      }).catch((error) => {
        this.logger.error('Renderer check failed:', error);
      });
    });

    // Setup window event handlers with proper cleanup tracking
    this.setupPanelWindowEventHandlers();

    // Add keyboard shortcut to open dev tools (F12)
    this.panelWindow.webContents.on('before-input-event', (_, input) => {
      if (input.key === 'F12') {
        this.panelWindow?.webContents.toggleDevTools();
      }
    });

    this.logger.debug('Panel window created successfully');
  }

  /**
   * Enforce hardcoded panel dimensions with enhanced protection
   */
  private async enforceHardcodedDimensions(): Promise<void> {
    if (!this.panelWindow || this.panelWindow.isDestroyed()) {
      return;
    }

    const currentSize = this.panelWindow.getSize();
    if (currentSize[0] !== UIService.PANEL_WIDTH || currentSize[1] !== UIService.PANEL_HEIGHT) {
      // this.logger.debug(`Correcting panel size from ${currentSize[0]}x${currentSize[1]} to ${UIService.PANEL_WIDTH}x${UIService.PANEL_HEIGHT}`);

      // Set flag to prevent resize handler from triggering
      this.isSettingSize = true;

      try {
        // Force size with multiple enforcement strategies
        this.panelWindow.setSize(UIService.PANEL_WIDTH, UIService.PANEL_HEIGHT);
        this.panelWindow.setMinimumSize(UIService.PANEL_WIDTH, UIService.PANEL_HEIGHT);
        this.panelWindow.setMaximumSize(UIService.PANEL_WIDTH, UIService.PANEL_HEIGHT);
        this.panelWindow.setResizable(false);

        // Wait for size change to take effect
        await new Promise(resolve => setTimeout(resolve, 50));

        // Verify the size was actually set
        const verifySize = this.panelWindow.getSize();
        if (verifySize[0] !== UIService.PANEL_WIDTH || verifySize[1] !== UIService.PANEL_HEIGHT) {
          // this.logger.warn(`Size enforcement failed: expected ${UIService.PANEL_WIDTH}x${UIService.PANEL_HEIGHT}, got ${verifySize[0]}x${verifySize[1]}`);
        }

      } finally {
        this.isSettingSize = false;
      }
    }

    // Always re-enforce constraints to prevent future changes
    this.panelWindow.setMinimumSize(UIService.PANEL_WIDTH, UIService.PANEL_HEIGHT);
    this.panelWindow.setMaximumSize(UIService.PANEL_WIDTH, UIService.PANEL_HEIGHT);
    this.panelWindow.setResizable(false);
  }

  /**
   * Setup panel window event handlers with proper cleanup tracking
   */
  private setupPanelWindowEventHandlers(): void {
    if (!this.panelWindow) {
      this.logger.warn('Cannot setup event handlers: panel window not available');
      return;
    }

    // Clear any existing listeners first
    this.cleanupPanelWindowEventListeners();

    // Setup 'closed' event handler
    const closedHandler = () => {
      this.logger.debug('Panel window closed event triggered');
      this.cleanupPanelWindowEventListeners();
      delete (this as any).panelWindow;
      this.isVisible = false;
    };
    this.panelWindow.on('closed', closedHandler);
    this.panelWindowEventListeners.set('closed', closedHandler);

    // Setup 'blur' event handler
    const blurHandler = () => {
      if (!this.isPinned && this.isVisible) {
        this.logger.debug('Panel window blur - hiding panel');
        this.hidePanel();
      }
    };
    this.panelWindow.on('blur', blurHandler);
    this.panelWindowEventListeners.set('blur', blurHandler);

    // Setup 'resize' event handler with enhanced protection
    const resizeHandler = () => {
      // Prevent infinite loops when we're setting the size ourselves
      if (this.isSettingSize) {
        return;
      }

      const currentSize = this.panelWindow?.getSize();
      if (currentSize && (currentSize[0] !== UIService.PANEL_WIDTH || currentSize[1] !== UIService.PANEL_HEIGHT)) {
        // this.logger.debug(`Panel resize detected: ${currentSize[0]}x${currentSize[1]}, enforcing hardcoded dimensions`);

        // Set flag to prevent infinite loop
        this.isSettingSize = true;

        try {
          this.panelWindow?.setSize(UIService.PANEL_WIDTH, UIService.PANEL_HEIGHT);
          // Re-enforce constraints more aggressively
          this.panelWindow?.setMinimumSize(UIService.PANEL_WIDTH, UIService.PANEL_HEIGHT);
          this.panelWindow?.setMaximumSize(UIService.PANEL_WIDTH, UIService.PANEL_HEIGHT);
          this.panelWindow?.setResizable(false); // Force non-resizable again
        } finally {
          // Always clear the flag, even if an error occurs
          setTimeout(() => {
            this.isSettingSize = false;
          }, 100);
        }
      }
    };
    this.panelWindow.on('resize', resizeHandler);
    this.panelWindowEventListeners.set('resize', resizeHandler);

    this.logger.debug(`Setup ${this.panelWindowEventListeners.size} panel window event handlers`);
  }

  /**
   * Clean up panel window event listeners to prevent memory leaks
   */
  private cleanupPanelWindowEventListeners(): void {
    if (!this.panelWindow || this.panelWindow.isDestroyed()) {
      this.panelWindowEventListeners.clear();
      return;
    }

    for (const [eventName, handler] of this.panelWindowEventListeners) {
      try {
        this.panelWindow.removeListener(eventName as any, handler);
        this.logger.debug(`Removed panel window event listener: ${eventName}`);
      } catch (error) {
        this.logger.warn(`Failed to remove panel window event listener ${eventName}:`, error);
      }
    }

    this.panelWindowEventListeners.clear();
    this.logger.debug('Panel window event listeners cleanup completed');
  }

  private setupActivationEngine(): void {
    this.activationEngine = new ActivationEngine(
      this.configurationService,
      this.logger
    );

    this.activationEngine.on('activate', (args: ActivationEventArgs) => {
      const isPreview = args.data?.preview === true;
      const isPinned = args.data?.pinned === true;

      this.logger.info(`🔥 Panel activation requested! Source: ${args.source}, Preview: ${isPreview}, Pinned: ${isPinned}`);

      if (isPinned) {
        this.setPinned(true);
      }

      this.showPanel(isPreview);
    });

    this.activationEngine.on('deactivate', (args: ActivationEventArgs) => {
      const isPreview = args.data?.preview === true;

      // Only hide if it's a preview deactivation and panel is not pinned
      if (isPreview && !this.isPinned) {
        this.hidePanel();
      }
    });
  }

  private setupThemeManager(): void {
    this.themeManager = new ThemeManager(
      this.configurationService,
      this.logger
    );

    this.themeManager.on('theme-changed', (args: ThemeChangedEventArgs) => {
      this.logger.info(`🎨 Theme changed: ${args.theme} (dark: ${args.isDark})`);
      this.applyThemeToPanel(args);
    });
  }

  private setupEventHandlers(): void {
    // Configuration changes
    this.configurationService.on('configurationChanged', (args) => {
      this.onConfigurationChanged(args.newSettings);
    });

    // IPC handlers for renderer process - remove existing handlers first
    try {
      ipcMain.removeHandler('ui:setPinned');
      ipcMain.removeHandler('ui:isVisible');
      ipcMain.removeHandler('ui:isPinned');
      ipcMain.removeHandler('ui:setModalState');
      ipcMain.removeHandler('activation-click');
    } catch (error) {
      // Handlers may not exist yet, ignore
    }

    ipcMain.handle('ui:setPinned', (_, pinned: boolean) => {
      this.setPinned(pinned);
      if (this.activationEngine) {
        this.activationEngine.setPanelPinned(pinned);
      }
    });

    ipcMain.handle('ui:isVisible', () => {
      return this.isVisible;
    });

    ipcMain.handle('ui:isPinned', () => {
      return this.isPinned;
    });

    ipcMain.handle('ui:setModalState', (_, isModalOpen: boolean) => {
      return this.setModalState(isModalOpen);
    });

    // Handle widget restart after settings changes
    ipcMain.handle('ui:restartWidget', async () => {
      return this.restartWidget();
    });

    // **CRITICAL FIX: Handle immediate WebApp attachment**
    ipcMain.handle('ui:attachActiveWebApp', async () => {
      console.log('[SERVICE-LIFECYCLE] IPC: attachActiveWebApp called');
      return this.attachActiveWebApp();
    });

    // Handle activation clicks from the indicator
    ipcMain.on('activation-click', () => {
      if (this.activationEngine) {
        this.activationEngine.activatePanel();
      }
    });
  }

  private onConfigurationChanged(newSettings: AppSettings): void {
    if (!this.panelWindow) return;

    // Ensure panel maintains hardcoded dimensions regardless of settings changes
    const currentSize = this.panelWindow.getSize();
    const targetWidth = UIService.PANEL_WIDTH;
    const targetHeight = UIService.PANEL_HEIGHT;

    if (currentSize[0] !== targetWidth || currentSize[1] !== targetHeight) {
      this.panelWindow.setSize(targetWidth, targetHeight);
      this.logger.debug(`Panel size enforced to hardcoded dimensions: ${targetWidth}x${targetHeight}`);

      // Update activation engine with new panel bounds
      this.updateActivationEnginePanelBounds();
    }

    // Update position if panel position changed
    const newPosition = this.isVisible
      ? this.calculateVisiblePosition(newSettings)
      : this.calculateHiddenPosition(newSettings);

    this.panelWindow.setPosition(newPosition.x, newPosition.y);

    this.logger.debug('Panel updated from configuration change with hardcoded dimensions enforced');
  }

  private calculateVisiblePosition(settings: AppSettings): { x: number; y: number } {
    const display = screen.getPrimaryDisplay();
    const workArea = display.workArea;
    // Use hardcoded width for consistent positioning
    const panelWidth = UIService.PANEL_WIDTH;

    if (settings.ui.panelPosition === PanelPosition.Right) {
      return {
        x: workArea.x + workArea.width - panelWidth,
        y: workArea.y
      };
    } else {
      return {
        x: workArea.x,
        y: workArea.y
      };
    }
  }

  private calculateHiddenPosition(settings: AppSettings): { x: number; y: number } {
    const display = screen.getPrimaryDisplay();
    const workArea = display.workArea;
    // Use hardcoded width for consistent positioning
    const panelWidth = UIService.PANEL_WIDTH;

    if (settings.ui.panelPosition === PanelPosition.Right) {
      return {
        x: workArea.x + workArea.width, // Completely off-screen to the right
        y: workArea.y
      };
    } else {
      return {
        x: workArea.x - panelWidth, // Completely off-screen to the left
        y: workArea.y
      };
    }
  }

  private async animateToPosition(target: { x: number; y: number }, duration: number): Promise<void> {
    return new Promise((resolve) => {
      if (!this.panelWindow) {
        resolve();
        return;
      }

      const startPos = this.panelWindow.getPosition();
      const startTime = Date.now();
      
      const animate = () => {
        if (!this.panelWindow || this.panelWindow.isDestroyed()) {
          resolve();
          return;
        }

        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function (ease-out)
        const eased = 1 - Math.pow(1 - progress, 3);

        const startX = startPos?.[0] || 0;
        const startY = startPos?.[1] || 0;
        const currentX = startX + (target.x - startX) * eased;
        const currentY = startY + (target.y - startY) * eased;

        // Ensure values are valid numbers and handle -0 case
        let x = Math.round(isNaN(currentX) ? (isNaN(target.x) ? 0 : target.x) : currentX);
        let y = Math.round(isNaN(currentY) ? (isNaN(target.y) ? 0 : target.y) : currentY);

        // Fix -0 values that cause Electron errors
        if (Object.is(x, -0)) x = 0;
        if (Object.is(y, -0)) y = 0;

        // Additional validation
        if (!isFinite(x) || !isFinite(y)) {
          this.logger.error('Invalid position values:', { x, y, target, currentX, currentY });
          resolve();
          return;
        }

        try {
          this.panelWindow.setPosition(x, y);
        } catch (error) {
          this.logger.error('Error setting panel position:', error, { x, y, target });
          resolve();
          return;
        }

        if (progress < 1) {
          setTimeout(animate, 16); // ~60fps
        } else {
          resolve();
        }
      };

      // Show window before animation if hidden
      if (!this.panelWindow.isVisible()) {
        this.logger.debug('Showing panel window for animation');
        this.panelWindow.show();
      } else {
        this.logger.debug('Panel window already visible');
      }

      animate();
    });
  }

  private updateActivationEnginePanelBounds(): void {
    if (!this.panelWindow || !this.activationEngine) {
      return;
    }

    try {
      const bounds = this.panelWindow.getBounds();
      this.activationEngine.setPanelBounds(bounds);
    } catch (error) {
      this.logger.error('Failed to update activation engine panel bounds:', error);
    }
  }

  // **CRITICAL FIX: Enhanced WebApp Attachment with Service Status Tracking**
  async attachActiveWebApp(): Promise<void> {
    console.log('[SERVICE-LIFECYCLE] UIService.attachActiveWebApp called');

    if (!this.panelWindow || !this.webEngineService) {
      console.warn('[SERVICE-LIFECYCLE] WARNING: Cannot attach - panel window or web engine service not available');
      return;
    }

    // Don't attach during modal state
    if (this.isModalOpen) {
      console.log('[SERVICE-LIFECYCLE] Skipping attachment - modal is open');
      return;
    }

    try {
      const activeWebAppHost = this.webEngineService.getActiveWebAppHost();
      if (!activeWebAppHost || !activeWebAppHost.BrowserView) {
        console.warn('[SERVICE-LIFECYCLE] WARNING: No active WebAppHost or BrowserView to attach');
        return;
      }

      console.log(`[SERVICE-LIFECYCLE] Attaching WebApp: ${activeWebAppHost.app.name} (${activeWebAppHost.app.id})`);

      // Use hardcoded dimensions for consistent BrowserView sizing
      const panelWidth = UIService.PANEL_WIDTH;
      const panelHeight = UIService.PANEL_HEIGHT;
      const totalUIHeight = UIService.TOTAL_UI_HEIGHT;

      const webContentBounds = {
        x: 0,
        y: totalUIHeight, // Start below header and tab bar
        width: panelWidth,
        height: Math.max(0, panelHeight - totalUIHeight) // Ensure non-negative height
      };

      console.log(`[SERVICE-LIFECYCLE] Calculated bounds: ${JSON.stringify(webContentBounds)}`);

      // Validate bounds before applying
      if (webContentBounds.width <= 0 || webContentBounds.height <= 0) {
        console.error('[SERVICE-LIFECYCLE] ERROR: Invalid web content bounds calculated:', webContentBounds);
        return;
      }

      // Attach the BrowserView to the panel window
      console.log('[SERVICE-LIFECYCLE] Setting BrowserView on panel window...');
      this.panelWindow.setBrowserView(activeWebAppHost.BrowserView);
      activeWebAppHost.BrowserView.setBounds(webContentBounds);

      // Navigate to the app URL if not already loaded
      if (!activeWebAppHost.CurrentUrl && activeWebAppHost.app.url) {
        try {
          console.log(`[SERVICE-LIFECYCLE] Navigating to app URL: ${activeWebAppHost.app.url}`);
          await activeWebAppHost.navigate(activeWebAppHost.app.url);
        } catch (navError) {
          console.warn('[SERVICE-LIFECYCLE] WARNING: Navigation failed:', navError);
          // Don't fail the entire attachment for navigation errors
        }
      }

      console.log(`[SERVICE-LIFECYCLE] SUCCESS: WebApp ${activeWebAppHost.app.name} attached to panel with immediate content`);

    } catch (error) {
      console.error('[SERVICE-LIFECYCLE] ERROR: Failed to attach active web app:', error);

      // Try to recover by clearing any problematic BrowserView state
      try {
        this.panelWindow.setBrowserView(null);
        console.log('[SERVICE-LIFECYCLE] Cleared BrowserView due to attachment error');
      } catch (clearError) {
        console.error('[SERVICE-LIFECYCLE] ERROR: Failed to clear BrowserView after attachment error:', clearError);
      }
    }
  }

  async detachWebApp(): Promise<void> {
    if (!this.panelWindow) {
      return;
    }

    try {
      this.panelWindow.setBrowserView(null);
      this.logger.debug('Detached web app from panel');
    } catch (error) {
      this.logger.error('Failed to detach web app:', error);
    }
  }

  private async applyThemeToPanel(themeArgs: ThemeChangedEventArgs): Promise<void> {
    if (!this.panelWindow || this.panelWindow.isDestroyed()) {
      return;
    }

    try {
      // Get theme variables
      const variables = this.themeManager?.getThemeVariables() || {};

      // Apply theme variables to the renderer process
      await this.panelWindow.webContents.executeJavaScript(`
        (function() {
          const root = document.documentElement;
          const variables = ${JSON.stringify(variables)};

          for (const [property, value] of Object.entries(variables)) {
            root.style.setProperty(property, value);
          }

          // Add theme class to body
          document.body.className = document.body.className.replace(/theme-\\w+/g, '');
          document.body.classList.add('theme-${themeArgs.isDark ? 'dark' : 'light'}');

          // Dispatch theme change event for renderer
          window.dispatchEvent(new CustomEvent('theme-changed', {
            detail: {
              theme: '${themeArgs.theme}',
              isDark: ${themeArgs.isDark},
              source: '${themeArgs.source}'
            }
          }));
        })();
      `);

      this.logger.debug('Theme applied to panel successfully');

    } catch (error) {
      this.logger.error('Failed to apply theme to panel:', error);
    }
  }
}


